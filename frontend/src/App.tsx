import { useState, useEffect, useRef, useCallback } from "react";
import { IllustrationGenerator } from "@/components/IllustrationGenerator";
import { IllustrationWelcome } from "@/components/IllustrationWelcome";
import { Button } from "@/components/ui/button";

// 插画生成请求接口
interface IllustrationRequest {
  user_input: string;
  style_preference: string;
  num_panels?: number | null;
}

// 插画生成响应接口（匹配后端标准化格式）
interface IllustrationResponse {
  success: boolean;
  message: string;
  data?: {
    final_illustration?: string;
    processed_story?: string;
    characters?: any[];
    storyboards?: any[];
    generated_images?: any[];
  };
  error?: string;
}

// 生成开始响应接口
interface GenerationStartResponse {
  success: boolean;
  session_id: string;
  message: string;
  error?: string;
}

// 生成过程状态
interface GenerationProgress {
  stage: string;
  title: string;
  description: string;
  data?: any;
  completed: boolean;
}

export default function App() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState<GenerationProgress[]>([]);
  const [currentResult, setCurrentResult] = useState<IllustrationResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [hasStarted, setHasStarted] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const eventSourceRef = useRef<EventSource | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const MAX_RETRY_ATTEMPTS = 3;
  const RETRY_DELAY_BASE = 2000; // 2秒基础延迟

  // 清理EventSource连接和重试定时器
  const cleanupEventSource = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
    setConnectionStatus('disconnected');
    setRetryCount(0);
  }, []);

  // 监听进度更新（增强版包含重试机制）
  const listenToProgress = useCallback((sessionId: string, attempt: number = 0) => {
    const apiUrl = import.meta.env.DEV
      ? "http://localhost:8080"
      : "http://localhost:8080";

    // 如果达到最大重试次数，停止重试
    if (attempt >= MAX_RETRY_ATTEMPTS) {
      setError('连接失败，请稍后重试');
      setIsGenerating(false);
      setConnectionStatus('error');
      return;
    }

    // 清理旧连接
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    setConnectionStatus('connecting');
    setRetryCount(attempt);

    // 创建新的EventSource连接
    const eventSource = new EventSource(`${apiUrl}/api/progress/${sessionId}`);
    eventSourceRef.current = eventSource;

    eventSource.onopen = () => {
      console.log('进度监听连接已建立');
      setConnectionStatus('connected');
      setRetryCount(0); // 连接成功后重置重试计数
      // 如果是重连，不清空进度，让后端发送历史进度
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'progress') {
          // 处理进度更新
          console.log('收到进度更新:', data);
          setGenerationProgress(prev => {
            // 特殊处理图像生成进度 - 这些是实时进度更新，不是阶段性的
            if (data.node_name === 'image_generation_progress') {
              // 查找图像生成阶段并更新其描述
              const existing = prev.find(p => p.stage === 'unified_image_generator');
              if (existing) {
                return prev.map(p => 
                  p.stage === 'unified_image_generator'
                    ? { ...p, description: data.description, data: data.data }
                    : p
                );
              } else {
                // 如果图像生成阶段还不存在，创建它
                return [...prev, {
                  stage: 'unified_image_generator',
                  title: '🖼️ 图像生成',
                  description: data.description,
                  data: data.data,
                  completed: false
                }];
              }
            } else {
              // 处理常规阶段更新
              const existing = prev.find(p => p.stage === data.node_name);
              if (existing) {
                // 更新现有阶段
                return prev.map(p => 
                  p.stage === data.node_name 
                    ? { ...p, completed: data.status === 'completed', description: data.description, data: data.data }
                    : p
                );
              } else {
                // 添加新阶段
                return [...prev, {
                  stage: data.node_name,
                  title: data.title,
                  description: data.description,
                  data: data.data,
                  completed: data.status === 'completed'
                }];
              }
            }
          });
        } else if (data.type === 'result') {
          // 处理最终结果
          console.log('收到最终结果:', data);
          const processedResult = {
            success: data.success,
            message: data.data?.messages?.[0]?.content || '插画生成完成',
            final_illustration: data.data?.final_illustration,
            processed_story: data.data?.processed_story,
            characters: data.data?.characters || [],
            storyboards: data.data?.storyboards || [],
            generated_images: data.data?.generated_images || []
          };
          
          console.log('处理后的结果:', processedResult);
          setCurrentResult(processedResult);
          setIsGenerating(false);
          setConnectionStatus('disconnected');
          cleanupEventSource();
          
          if (!data.success) {
            setError(data.data?.error || '插画生成失败');
          }
        } else if (data.type === 'error') {
          // 处理错误
          setError(data.message || '生成过程出错');
          setIsGenerating(false);
          setConnectionStatus('error');
          cleanupEventSource();
        }
      } catch (err) {
        console.error('解析SSE数据失败:', err);
        // 数据解析错误不应该导致重试，只记录错误
      }
    };

    eventSource.onerror = (event) => {
      console.error('SSE连接错误:', event);
      setConnectionStatus('error');
      
      eventSource.close();
      eventSourceRef.current = null;
      
      // 如果还在生成中，尝试重连
      if (isGenerating) {
        const nextAttempt = attempt + 1;
        if (nextAttempt < MAX_RETRY_ATTEMPTS) {
          const delay = RETRY_DELAY_BASE * Math.pow(2, attempt); // 指数退避
          console.log(`将在 ${delay}ms 后重试连接（第 ${nextAttempt + 1} 次尝试）`);
          
          retryTimeoutRef.current = setTimeout(() => {
            listenToProgress(sessionId, nextAttempt);
          }, delay);
        } else {
          setError('连接中断，请重新开始');
          setIsGenerating(false);
        }
      }
    };
  }, [isGenerating, cleanupEventSource]);

  // 组件卸载时清理连接
  useEffect(() => {
    return cleanupEventSource;
  }, [cleanupEventSource]);

  // 模拟生成过程的进度更新（保留作为备用）
  const simulateProgress = useCallback((stages: string[]) => {
    stages.forEach((stage, index) => {
      setTimeout(() => {
        setGenerationProgress(prev => [
          ...prev,
          {
            stage: stage,
            title: getStageTitle(stage),
            description: getStageDescription(stage),
            completed: false
          }
        ]);
      }, index * 1000);
    });
  }, []);

  // 获取阶段标题（更新为匹配后端节点名称）
  const getStageTitle = (stage: string): string => {
    const titles: Record<string, string> = {
      'input_handler': '📝 故事处理',
      'story_splitter': '📚 故事分段', 
      'character_extractor': '👥 角色提取',
      'character_image_generator': '🎨 角色基准图生成',
      'storyboard_generator': '🎬 分镜生成',
      'scene_prompt_optimizer': '✨ 提示词优化',
      'unified_image_generator': '🖼️ 图像生成',
      'image_generation_progress': '🎨 图像生成进度',
      'image_merger': '🔗 图像合并',
      'finalize_illustration': '🎉 完成'
    };
    return titles[stage] || stage;
  };

  // 获取阶段描述（更新为匹配后端节点名称）
  const getStageDescription = (stage: string): string => {
    const descriptions: Record<string, string> = {
      'input_handler': '正在分析和优化您的故事内容...',
      'story_splitter': '正在将故事分解为适合插画的分段...',
      'character_extractor': '正在识别故事中的角色并提取特征...',
      'character_image_generator': '正在生成角色基准图以保持一致性...',
      'storyboard_generator': '正在创建详细的分镜描述...',
      'scene_prompt_optimizer': '正在优化AI绘画提示词...',
      'unified_image_generator': '正在生成插画图片...',
      'image_generation_progress': '图像生成进度更新...',
      'image_merger': '正在合并图片为最终插画...',
      'finalize_illustration': '插画生成完成！'
    };
    return descriptions[stage] || '处理中...';
  };

  // 调用插画生成API（新的SSE版本）
  const generateIllustration = useCallback(async (request: IllustrationRequest) => {
    setIsGenerating(true);
    setError(null);
    setGenerationProgress([]);
    setCurrentResult(null);

    try {
      const apiUrl = import.meta.env.DEV
        ? "http://localhost:8080"
        : "http://localhost:8080";

      // 第一步：启动生成任务
      const response = await fetch(`${apiUrl}/api/start-generation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const startResult: GenerationStartResponse = await response.json();

      if (!startResult.success) {
        throw new Error(startResult.error || '启动生成失败');
      }

      // 第二步：开始监听进度
      setCurrentSessionId(startResult.session_id);
      listenToProgress(startResult.session_id);

    } catch (err) {
      setError(err instanceof Error ? err.message : '网络错误');
      setIsGenerating(false);
    }
  }, [listenToProgress]);

  const handleSubmit = useCallback(
    (userInput: string, stylePreference: string, numPanels: number | null) => {
      if (!userInput.trim()) return;

      setHasStarted(true);

      const request: IllustrationRequest = {
        user_input: userInput,
        style_preference: stylePreference,
        ...(numPanels !== null && { num_panels: numPanels }),
      };

      generateIllustration(request);
    },
    [generateIllustration]
  );

  const handleCancel = useCallback(() => {
    setIsGenerating(false);
    setGenerationProgress([]);
    setError(null);
    setRetryCount(0);
    setConnectionStatus('disconnected');
    cleanupEventSource();
  }, [cleanupEventSource]);

  const handleReset = useCallback(() => {
    setHasStarted(false);
    setIsGenerating(false);
    setGenerationProgress([]);
    setCurrentResult(null);
    setError(null);
    setCurrentSessionId(null);
    setRetryCount(0);
    setConnectionStatus('disconnected');
    cleanupEventSource();
  }, [cleanupEventSource]);

  return (
    <div className="flex h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white font-sans antialiased">
      <main className="h-full w-full max-w-6xl mx-auto">
        {!hasStarted ? (
          <IllustrationWelcome
            onSubmit={handleSubmit}
            isLoading={isGenerating}
          />
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="flex flex-col items-center justify-center gap-4 bg-red-900/20 p-8 rounded-lg border border-red-500/30">
              <h1 className="text-2xl text-red-400 font-bold">生成失败</h1>
              <p className="text-red-300 text-center max-w-md">{error}</p>
              <div className="flex gap-4">
                <Button
                  variant="destructive"
                  onClick={handleReset}
                >
                  重新开始
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                >
                  刷新页面
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <IllustrationGenerator
            isGenerating={isGenerating}
            progress={generationProgress}
            result={currentResult}
            onCancel={handleCancel}
            onReset={handleReset}
          />
        )}
      </main>
    </div>
  );
}
