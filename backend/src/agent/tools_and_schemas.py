from typing import List, Optional
from pydantic import BaseModel, Field


# 插画生成相关的模式定义

class TranslationResult(BaseModel):
    """翻译结果"""
    original_text: str = Field(description="原始文本")
    translated_text: str = Field(description="翻译后的文本")
    source_language: str = Field(description="源语言")
    target_language: str = Field(description="目标语言")


class StoryProcessingResult(BaseModel):
    """故事处理结果"""
    processed_story: str = Field(description="处理后的故事内容")
    processed_story_en: str = Field(description="处理后的故事内容（英文版）")
    story_type: str = Field(description="故事类型：summary或expansion")
    rationale: str = Field(description="处理决策的理由")


class StorySegment(BaseModel):
    """故事分段"""
    segment_id: int = Field(description="分段ID")
    content: str = Field(description="分段内容")
    content_en: str = Field(description="分段内容（英文版）")
    key_elements: List[str] = Field(description="关键元素")


class StorySegmentationResult(BaseModel):
    """故事分段结果"""
    segments: List[StorySegment] = Field(description="故事分段列表")
    total_segments: int = Field(description="总分段数")


class CharacterInfo(BaseModel):
    """角色信息"""
    name: str = Field(description="角色名称")
    description: str = Field(description="角色描述")
    appearance: str = Field(description="外貌特征英文描述")
    appearance_cn: str = Field(description="外貌特征中文描述")
    style: str = Field(description="风格设定英文描述")
    style_cn: str = Field(description="风格设定中文描述")
    role: str = Field(description="角色类型：main/supporting")


class CharacterExtractionResult(BaseModel):
    """角色提取结果"""
    characters: List[CharacterInfo] = Field(description="角色列表")
    main_character: Optional[CharacterInfo] = Field(description="主角信息")


class StoryboardPanel(BaseModel):
    """分镜面板"""
    panel_id: str = Field(description="分镜ID")
    scene_description: str = Field(description="场景描述")
    characters_involved: List[str] = Field(description="涉及的角色名称")
    environment: str = Field(description="环境描述")
    action: str = Field(description="动作描述")
    mood: str = Field(description="氛围")
    camera_angle: str = Field(description="镜头角度")


class StoryboardResult(BaseModel):
    """分镜结果"""
    panels: List[StoryboardPanel] = Field(description="分镜面板列表")
    style_notes: str = Field(description="整体风格说明")


class ScenePrompt(BaseModel):
    """场景提示词"""
    panel_id: str = Field(description="对应的分镜ID")
    prompt: str = Field(description="优化后的提示词")
    style_tags: List[str] = Field(description="风格标签")
    generation_type: str = Field(description="生成类型：text2img或multi_edit")
    characters_involved: List[str] = Field(description="涉及的角色名称列表", default=[])


class ScenePromptResult(BaseModel):
    """场景提示词结果"""
    prompts: List[ScenePrompt] = Field(description="场景提示词列表")


class ImageGenerationResult(BaseModel):
    """图像生成结果"""
    panel_id: int = Field(description="分镜ID")
    image_url: str = Field(description="生成的图片URL")
    prompt_used: str = Field(description="使用的提示词")
    generation_type: str = Field(description="生成类型")
    success: bool = Field(description="生成是否成功")


# 原有的搜索相关模式（保留兼容性）

class SearchQueryList(BaseModel):
    query: List[str] = Field(
        description="A list of search queries to be used for web research."
    )
    rationale: str = Field(
        description="A brief explanation of why these queries are relevant to the research topic."
    )


class Reflection(BaseModel):
    is_sufficient: bool = Field(
        description="Whether the provided summaries are sufficient to answer the user's question."
    )
    knowledge_gap: str = Field(
        description="A description of what information is missing or needs clarification."
    )
    follow_up_queries: List[str] = Field(
        description="A list of follow-up queries to address the knowledge gap."
    )


class PanelCountAnalysisResult(BaseModel):
    """分镜数量分析结果"""
    recommended_panels: int = Field(description="推荐的分镜数量", ge=1, le=20)
    analysis_reason: str = Field(description="分析理由")
    complexity_score: int = Field(description="复杂度评分", ge=1, le=10)
    story_structure: str = Field(description="故事结构类型")
    key_elements: List[str] = Field(description="关键元素列表")
